# iOS VPN Extension Keep-Alive 功能实现

## 概述

为了防止iOS VPN extension进入frozen状态，我们实现了定期15秒的服务器列表获取功能。这个功能纯粹用于保活，不会更新任何内容。

## 实现细节

### 1. 新增属性

在 `VPNPacketTunnelProvider` 类中添加了以下属性：

```swift
// MARK: - Keep-Alive Server List Fetching
private var keepAliveTimer: DispatchSourceTimer?
private var serverListUrl: String?
private let keepAliveInterval: TimeInterval = 15.0 // 15 seconds
```

### 2. 核心方法

#### startKeepAliveServerListFetching()
- 在VPN tunnel成功启动后调用
- 从App Group获取服务器列表URL
- 创建15秒间隔的定时器
- 使用DispatchSourceTimer确保性能和控制

#### stopKeepAliveServerListFetching()
- 在VPN tunnel停止时调用
- 取消定时器并清理资源

#### performKeepAliveServerListFetch()
- 执行实际的HTTP请求
- 设置10秒超时
- 使用特殊User-Agent标识：`VPN-Extension-KeepAlive/1.0`
- 丢弃响应内容，仅用于保活

### 3. 集成点

#### VPN启动时
在 `startTunnel` 方法成功完成后：
```swift
// Start keep-alive server list fetching to prevent extension from being frozen
startKeepAliveServerListFetching()
```

#### VPN停止时
在 `stopTunnel` 方法开始时：
```swift
// Stop keep-alive server list fetching
stopKeepAliveServerListFetching()
```

### 4. App Group集成

#### 主应用端
在 `handleSetServerProviderUrl` 方法中，成功设置服务器列表URL后：
```swift
// Store server list URL in App Group for VPN extension keep-alive access
if let appGroupDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient") {
    appGroupDefaults.set(url, forKey: "server_list_url")
    appGroupDefaults.synchronize()
}
```

#### VPN Extension端
从App Group读取URL：
```swift
if let appGroupDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient") {
    serverListUrl = appGroupDefaults.string(forKey: "server_list_url")
}
```

## 技术特点

### 1. 性能优化
- 使用 `DispatchSourceTimer` 而非 `Timer.scheduledTimer`
- 在 `.utility` QoS队列中执行，避免影响主线程
- 10秒HTTP超时，避免长时间阻塞

### 2. 错误处理
- 如果没有配置URL，跳过keep-alive
- HTTP请求失败时仅记录debug日志，不影响VPN功能
- 使用weak self避免循环引用

### 3. 资源管理
- 定时器在VPN停止时自动清理
- 使用App Group共享URL，避免硬编码

### 4. 日志记录
- 启动/停止时记录info级别日志
- HTTP请求结果记录debug级别日志
- 包含URL和状态码等关键信息

## 使用场景

1. **VPN连接期间**：每15秒自动发送HTTP请求保活
2. **后台运行**：防止iOS系统冻结VPN extension
3. **无网络影响**：请求失败不影响VPN正常功能
4. **动态配置**：支持UI动态设置服务器列表URL

## 注意事项

1. 仅在VPN连接状态下运行
2. 依赖主应用设置的服务器列表URL
3. 纯保活功能，不处理响应内容
4. 使用App Group进行进程间通信

## 文件修改

- `ui/flutter/ItForceCore/Sources/Platform/NetworkExtension/PacketTunnelProvider.swift`
- `ui/flutter/ios/Runner/PlatformChannelHandler.swift` (已有App Group存储代码)

## 测试验证

编译成功，无编译错误或警告。功能将在VPN连接时自动启用。
